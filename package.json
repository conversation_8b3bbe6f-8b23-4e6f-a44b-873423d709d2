{"name": "ai-chat", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build --turbopack", "check": "next lint && bun typecheck", "dev": "next dev --turbo", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "format:write": "prettier --write \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "lint": "next lint", "lint:fix": "next lint --fix", "preview": "next build && next start", "start": "next start", "typecheck": "tsgo --noEmit"}, "dependencies": {"@ai-sdk/deepseek": "^1.0.2", "@ai-sdk/google": "^2.0.2", "@ai-sdk/openai": "^2.0.3", "@ai-sdk/react": "^2.0.4", "@axiomhq/js": "^1.3.1", "@axiomhq/logging": "^0.1.4", "@axiomhq/nextjs": "^0.1.4", "@axiomhq/react": "^0.1.4", "@base-ui-components/react": "^1.0.0-beta.2", "@clerk/nextjs": "^6.28.1", "@clerk/react-router": "^1.8.9", "@clerk/themes": "^2.4.4", "@convex-dev/crons": "^0.1.9", "@convex-dev/polar": "^0.6.1", "@convex-dev/r2": "^0.7.1", "@convex-dev/react-query": "^0.0.0-alpha.11", "@nivo/calendar": "^0.99.0", "@polar-sh/nextjs": "^0.4.4", "@polar-sh/sdk": "^0.34.9", "@sentry/nextjs": "10", "@t3-oss/env-nextjs": "^0.13.8", "@tanstack/react-pacer": "^0.14.0", "@tanstack/react-query": "^5.84.1", "@tanstack/react-virtual": "^3.13.12", "@uidotdev/usehooks": "^2.4.1", "@vercel/analytics": "^1.5.0", "@vercel/functions": "^2.2.8", "@vercel/speed-insights": "^1.2.0", "ai": "^5.0.4", "botid": "^1.4.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "convex": "^1.25.4", "convex-helpers": "^0.1.101", "ioredis": "^5.7.0", "katex": "^0.16.22", "lodash.throttle": "^4.1.1", "lucide-react": "^0.536.0", "marked": "^16.1.2", "next": "^15.4.5", "next-themes": "^0.4.6", "posthog-js": "^1.258.5", "posthog-node": "^5.6.0", "radix-ui": "^1.4.2", "react": "^19.1.1", "react-dom": "^19.1.1", "react-markdown": "^10.1.0", "react-router": "^7.7.1", "react-shiki": "^0.7.2", "rehype-katex": "^7.0.1", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "resumable-stream": "^2.2.3", "server-only": "^0.0.1", "shiki": "^3.9.2", "sonner": "^2.0.7", "svix": "^1.70.0", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.3.6", "uuid": "^11.1.0", "zod": "^4.0.14", "zustand": "^5.0.7"}, "devDependencies": {"@convex-dev/eslint-plugin": "^0.0.1-alpha.4", "@eslint/eslintrc": "^3.3.1", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/typography": "^0.5.16", "@types/lodash.throttle": "^4.1.9", "@types/node": "^24.2.0", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "@typescript/native-preview": "^7.0.0-dev.20250805.1", "babel-plugin-react-compiler": "^19.1.0-rc.2", "eslint": "^9.32.0", "eslint-config-next": "^15.4.5", "eslint-plugin-react-hooks": "^6.0.0-rc.1", "postcss": "^8.5.6", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^4.1.11", "typescript": "^5.9.2", "typescript-eslint": "^8.39.0"}, "ct3aMetadata": {"initVersion": "7.39.3"}, "trustedDependencies": ["@clerk/shared", "@tailwindcss/oxide", "@vercel/speed-insights", "core-js", "esbuild", "sharp"]}