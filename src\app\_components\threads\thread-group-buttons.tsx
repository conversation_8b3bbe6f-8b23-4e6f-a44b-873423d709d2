import { PlusIcon } from "lucide-react";
import { NavLink } from "react-router";

import { But<PERSON> } from "../ui/button";
import { SidebarTrigger, useSidebar } from "../ui/sidebar";
import { ThreadCommand } from "./thread-command";

import { cn } from "@/lib/utils";

export function ThreadGroupButtons() {
  const { state, isMobile } = useSidebar();

  return (
    <div
      data-state={isMobile ? "collapsed" : state}
      className={cn(
        "absolute top-3 left-3 z-50 flex items-center justify-center gap-1 rounded-md p-1",
        "bg-sidebar group data-[state=collapsed]:!bg-sidebar md:bg-transparent",
      )}
    >
      <SidebarTrigger />
      <ThreadCommand />

      <Button
        asChild
        size="icon"
        variant="ghost"
        className="size-7 group-data-[state=expanded]:hidden"
      >
        <NavLink to="/">
          <PlusIcon />
          <span className="sr-only">New Thread</span>
        </NavLink>
      </Button>
    </div>
  );
}
