import { useEffect } from "react";
import { useNavigate } from "react-router";

import { toast } from "sonner";
import { v4 as uuidv4 } from "uuid";

import { abortChatRequest } from "@/lib/chat/send-chat-request";
import { useChatStore } from "@/lib/chat/store";

const THREAD_COMMAND_KEYBOARD_SHORTCUT = "k";
const NEW_THREAD_KEYBOARD_SHORTCUT = "o";

export function RegisterHotkeys() {
  const navigate = useNavigate();

  const status = useChatStore((state) => state.status);
  const isStreaming = useChatStore((state) => state.isStreaming);
  const editMessage = useChatStore((state) => state.editMessage);

  const setEditMessage = useChatStore((state) => state.setEditMessage);
  const setThreadCommandOpen = useChatStore((state) => state.setThreadCommandOpen);
  const addAttachment = useChatStore((state) => state.addAttachment);
  const setChatInput = useChatStore((state) => state.setChatInput);

  useEffect(() => {
    function onPaste(event: ClipboardEvent) {
      // Handle pasted files (existing behavior - unchanged)
      if (event.clipboardData?.files.length) {
        const files = Array.from(event.clipboardData.files ?? []);

        const acceptFiles = files.filter(
          (file) => file.type.includes("image") || file.type.includes("pdf"),
        );

        if (acceptFiles.length > 0) {
          const attachments = acceptFiles.map((file) => {
            let type: "image" | "pdf" = "image";
            if (file.type.includes("pdf")) type = "pdf";

            return { id: uuidv4(), name: file.name, size: file.size, file, type };
          });

          addAttachment(attachments);
        }

        if (acceptFiles.length < files.length) {
          toast.error("File type not supported", {
            description: "Please upload an image or PDF file.",
          });
        }

        // Do not proceed to text paste when files are present
        return;
      }

      // If no files were pasted, handle plain text paste into chat input
      const text = event.clipboardData?.getData("text") ?? "";
      if (!text) return;

      const chatInput = document.getElementById("textarea-chat-input");
      if (!chatInput) return;

      setChatInput((prev) => prev + text);
      chatInput.focus();
    }

    function handleKeyboardShortcut(event: KeyboardEvent) {
      const target = event.target as HTMLElement;
      if (
        !event.ctrlKey &&
        !event.metaKey &&
        !event.altKey &&
        event.key.length === 1 &&
        target.tagName !== "INPUT" &&
        target.tagName !== "TEXTAREA" &&
        !target.isContentEditable
      ) {
        const chatInput = document.getElementById("textarea-chat-input");
        if (chatInput) chatInput.focus();
      }

      if (event.key === "Escape") {
        if (status === "pending" || isStreaming) {
          event.preventDefault();
          void abortChatRequest();
        } else if (editMessage) {
          event.preventDefault();
          setEditMessage(null);
        }
      }

      if (
        event.key.toLowerCase() === THREAD_COMMAND_KEYBOARD_SHORTCUT &&
        (event.metaKey || event.ctrlKey)
      ) {
        event.preventDefault();
        setThreadCommandOpen((open) => !open);
      }

      if (
        event.key.toLowerCase() === NEW_THREAD_KEYBOARD_SHORTCUT &&
        event.shiftKey &&
        (event.metaKey || event.ctrlKey)
      ) {
        event.preventDefault();
        void navigate("/");
      }
    }

    window.addEventListener("keydown", handleKeyboardShortcut);
    window.addEventListener("paste", onPaste);
    return () => {
      window.removeEventListener("keydown", handleKeyboardShortcut);
      window.removeEventListener("paste", onPaste);
    };
  }, [
    editMessage,
    isStreaming,
    navigate,
    setEditMessage,
    setThreadCommandOpen,
    status,
    addAttachment,
    setChatInput,
  ]);

  return null;
}
